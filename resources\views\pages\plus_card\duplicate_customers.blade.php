@extends('pages.build')
@section('title','Aynı Kart No ile Birden Fazla Müşteri')
@php
    if (!function_exists('pastelColor')) {
        function pastelColor($seed) {
            srand(crc32($seed));
            $h = rand(0, 360);
            $s = 60 + rand(0, 20); // 60-80
            $l = 85 + rand(0, 10); // 85-95
            return "hsl($h, $s%, $l%)";
        }
    }
@endphp
@push('css')
<style>
        table.table tbody tr td {
        height: 10px !important;
    }

    table.table tbody td {
        height: 10px !important;
        padding-top: 0 !important;
        padding-bottom: 0 !important;
        line-height: 10px !important;
        font-size: 10px !important;
        vertical-align: middle !important;
    }

    

    .form-check-label,
    .form-check-input {
        transform: scale(0.8);
        margin: 0;
    }

    .primary-radio {
        width: 10px !important;
        height: 10px !important;
        transform: scale(1) !important;
        margin-right: 4px !important;

        appearance: none;
        -webkit-appearance: none;
        -moz-appearance: none;
        border-radius: 50%;
        background-color: #9dada1ff; /* yeşil */
        border: 1px solid #ccc;
        cursor: pointer;
        position: relative;
    }

    .primary-radio:checked::before {
        content: '';
        position: absolute;
        top: 2px;
        left: 2px;
        width: 6px;
        height: 6px;
        background-color: white;
        border-radius: 50%;
    }

    textarea.form-control {
        font-size: 10px;
    }

    .btn {
        padding: 0.15rem 0.3rem;
        font-size: 10px;
    }
</style>
@endpush

@section('content')
<div class="row">
    <div class="col-xl-12">
        <div class="card custom-card">
            <div class="card-body">
                <h4>Aynı Kart No ile Birden Fazla Müşteri</h4>
                <div class="table-responsive">
                    <table class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>Kart No</th>
                                <th>Müşteri ID</th>
                                <th>Müşteri Unvanı</th>
                                <th>Telefon Numarası</th>
                                <th>Kart Bayi</th>
                                <th>Son Yükleyen Bayi</th>
                                <th>İşlem</th>
                            </tr>
                        </thead>
                        <tbody>
                        @forelse($items as $no => $group)
                            <tr>
                                <td colspan="8" style="font-weight:bold; color:#333; height:30px; line-height:30px; padding:0;">
                                    Kart No: {{ $no }}
                                </td>
                            </tr>

                           @foreach($group as $item)
    @php
        $isPrimary = $item->is_primary;
        $groupName = "primarySelect_" . $no;
    @endphp
    <tr style="">
        <td>{{ $loop->iteration }}</td>
        <td>{{ $item->no }}</td>
        <td>{{ $item->customer_id }}</td>
       <td>
    <div class="d-flex justify-content-center align-items-center gap-2">
        <span>{{ optional($item->getCustomer)->unvan }}</span>
        <div>
            <input class="form-check-input primary-radio"
                   type="radio"
                   name="{{ $groupName }}"
                   data-customer-id="{{ $item->customer_id }}"
                   data-card-no="{{ $item->no }}"
                   {{ $isPrimary ? 'checked' : '' }}>
            <label class="form-check-label ms-1">Birincil Kayıt Seç</label>
        </div>
    </div>
</td>

        <td>{{ optional($item->getCustomer)->telefon }}</td>
        <td>{{ optional($item->getBranch)->kisa_ad ?? '-' }}</td>
        <td>{{ $item->son_islem_bayi ?? '-' }}</td>
        <td>
            <button class="btn btn-primary note-btn"
                    data-bs-toggle="modal"
                    data-bs-target="#noteModal{{ $item->customer_id }}_{{ $item->no }}">
                Not Ekle
            </button>

            <div class="modal fade"
                 id="noteModal{{ $item->customer_id }}_{{ $item->no }}"
                 tabindex="-1"
                 aria-labelledby="noteModalLabel{{ $item->customer_id }}_{{ $item->no }}"
                 aria-hidden="true">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="noteModalLabel{{ $item->customer_id }}_{{ $item->no }}">
                                Not Ekle (Müşteri ID: {{ $item->customer_id }}, Kart No: {{ $item->no }})
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Kapat"></button>
                        </div>
                        <div class="modal-body">
                            <textarea id="noteText{{ $item->customer_id }}_{{ $item->no }}" class="form-control" rows="4" placeholder="Notunuzu giriniz...">{{ $item->note ?? '' }}</textarea>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Kapat</button>
                            <button type="button"
                                    class="btn btn-primary save-note-btn"
                                    data-customer-id="{{ $item->customer_id }}"
                                    data-card-no="{{ $item->no }}">
                                Kaydet
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </td>
    </tr>
@endforeach

                        @empty
                            <tr><td colspan="8">Kayıt bulunamadı.</td></tr>
                        @endforelse
                        </tbody>
                    </table>

                    <div class="mt-3 d-flex justify-content-center">
                        {{ $items->links('pagination::bootstrap-5') }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function () {
    document.querySelectorAll('.primary-radio').forEach(function (radio) {
        radio.addEventListener('change', function () {
            var cardNo = this.getAttribute('data-card-no');
            var customerId = this.getAttribute('data-customer-id');

            axios.post('{{ route('plus-card.set-primary') }}', {
                card_no: cardNo,
                customer_id: customerId,
                type: 'duplicate_customers',
                _token: '{{ csrf_token() }}'
            }).then(function (response) {
                if (response.data.success) {
                    document.querySelectorAll('input[name="primarySelect_' + cardNo + '"]').forEach(function (el) {
                        el.checked = false;
                    });
                    document.querySelector('input[name="primarySelect_' + cardNo + '"][data-customer-id="' + customerId + '"]').checked = true;
                    alert('Birincil kayıt olarak ayarlandı!');
                } else {
                    alert('Seçim hatası: ' + (response.data.message || 'Bilinmeyen hata'));
                }
            }).catch(function () {
                alert('İşlem sırasında hata oluştu.');
            });
        });
    });

    document.querySelectorAll('.save-note-btn').forEach(function (btn) {
        btn.addEventListener('click', function () {
            var customerId = this.getAttribute('data-customer-id');
            var cardNo = this.getAttribute('data-card-no');
            var note = document.getElementById('noteText' + customerId + '_' + cardNo).value;

            axios.post('/plus-card/customer-note', {
                key: 'customer_' + customerId + '_pluscard',
                note: note,
                customer_id: customerId,
                card_no: cardNo,
                _token: '{{ csrf_token() }}'
            }).then(function (response) {
                if (response.data.success) {
                    alert('Not kaydedildi!');
                    var modalElement = document.getElementById('noteModal' + customerId + '_' + cardNo);
                    var modal = bootstrap.Modal.getInstance(modalElement);
                    if (modal) {
                        modal.hide();
                    } else {
                        modal = new bootstrap.Modal(modalElement);
                        modal.hide();
                    }
                } else {
                    alert('Kayıt başarısız: ' + (response.data.message || 'Bilinmeyen hata'));
                }
            }).catch(function () {
                alert('Kayıt sırasında hata oluştu!');
            });
        });
    });
});
</script>
@endsection
