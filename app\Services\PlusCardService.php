<?php

namespace App\Services;

use Illuminate\Support\Facades\DB;
use App\Models\PlusCard;
use App\Models\PlusCardCrediAndPuanAdd;
use App\Models\User;
use Illuminate\Pagination\LengthAwarePaginator;

class PlusCardService
{
    /**
     * Aynı no'ya sahip ve birden fazla farklı customer_id'ye sahip kartları getirir
     *
     * @param int $perPage
     * @param array|null $branchIds
     * @return \Illuminate\Pagination\LengthAwarePaginator
     */
    public function getDuplicateNoCardsGrouped($perPage, $branchIds = null)
    {
        $duplicateNos = PlusCard::select('no')
            ->whereNull('deleted_at')
            ->when($branchIds !== null && !empty($branchIds), function ($query) use ($branchIds) {
                return $query->whereIn('branch_id', $branchIds);
            })
            ->groupBy('no')
            ->havingRaw('COUNT(DISTINCT customer_id) > 1')
            ->orderBy('no')
            ->get()
            ->pluck('no');

        $grouped = PlusCard::select([
                'plus_cards.*',
                'last_transaction_branch.kisa_ad as son_islem_bayi'
            ])
            ->whereIn('no', $duplicateNos)
            ->with(['getBranch', 'getCustomer'])
            ->leftJoin('plus_card_credi_and_puan_add as last_transaction', function($join) {
                $join->on('last_transaction.card_id', '=', 'plus_cards.id')
                     ->whereRaw('last_transaction.id = (SELECT MAX(id) FROM plus_card_credi_and_puan_add WHERE card_id = plus_cards.id AND deleted_at IS NULL)');
            })
            ->leftJoin('users as last_transaction_user', 'last_transaction_user.id', '=', 'last_transaction.user_id')
            ->leftJoin('branches as last_transaction_branch', 'last_transaction_branch.id', '=', 'last_transaction_user.branch_id')
            ->whereNull('plus_cards.deleted_at')
            ->when($branchIds !== null && !empty($branchIds), function ($query) use ($branchIds) {
                return $query->whereIn('plus_cards.branch_id', $branchIds);
            })
            ->orderBy('plus_cards.no')
            ->orderBy('plus_cards.customer_id')
            ->get()
            ->groupBy('no');

        $currentPage = LengthAwarePaginator::resolveCurrentPage('page');
        $groupsPerPage = $grouped->forPage($currentPage, $perPage);

        $paginated = new LengthAwarePaginator(
            $groupsPerPage,
            $grouped->count(), // toplam grup sayısı
            $perPage,
            $currentPage,
            ['path' => request()->url(), 'query' => request()->query()]
        );

        return $paginated;
    }

    /**
     * Birden fazla pluscard'ı olan müşterileri getirir
     *
     * @param int $perPage
     * @param array|null $branchIds
     * @return \Illuminate\Pagination\LengthAwarePaginator
     */
    public function getCustomersWithMultipleCards($perPage = 10, $branchIds = null)
    {
        $query = PlusCard::query()
            ->select([
                'plus_cards.customer_id',
                'customers.ad',
                'customers.soyad',
                'customers.telefon',
                'customers.unvan as musteri_unvani',
                DB::raw('COUNT(plus_cards.customer_id) as kart_sayisi'),
                DB::raw("GROUP_CONCAT(plus_cards.no SEPARATOR ', ') as kart_numaralari"),
                DB::raw("GROUP_CONCAT(branches.kisa_ad SEPARATOR ', ') as bayi_ismi"),
                DB::raw("GROUP_CONCAT(plus_cards.is_primary SEPARATOR ', ') as primary_status"),
                DB::raw("GROUP_CONCAT(DISTINCT CASE
                    WHEN last_upload_user.user_role_group_id = 30 THEN
                        (SELECT kisa_ad FROM branches WHERE id = 26)
                    ELSE
                        first_user_branch.kisa_ad
                END SEPARATOR ', ') as yukleme_yapan_sube"),
                DB::raw("GROUP_CONCAT(last_transaction_branch.kisa_ad ORDER BY plus_cards.id SEPARATOR ', ') as son_islem_bayi")
            ])
            ->join('customers', 'customers.id', '=', 'plus_cards.customer_id')
            ->leftJoin('branches', 'branches.id', '=', 'plus_cards.branch_id')
            ->leftJoin('plus_card_credi_and_puan_add as last_upload', function($join) {
                $join->on('last_upload.card_id', '=', 'plus_cards.id')
                     ->whereRaw('last_upload.id = (SELECT MAX(id) FROM plus_card_credi_and_puan_add WHERE card_id = plus_cards.id AND deleted_at IS NULL)');
            })
            ->leftJoin('users as last_upload_user', 'last_upload_user.id', '=', 'last_upload.user_id')
            ->leftJoin('user_branches2 as first_user_branch_pivot', function($join) {
                $join->on('first_user_branch_pivot.user_id', '=', 'last_upload_user.id')
                     ->whereNull('first_user_branch_pivot.deleted_at')
                     ->whereRaw('first_user_branch_pivot.id = (SELECT MIN(id) FROM user_branches2 WHERE user_id = last_upload_user.id AND deleted_at IS NULL)');
            })
            ->leftJoin('branches as first_user_branch', 'first_user_branch.id', '=', 'first_user_branch_pivot.branch_id')
            ->leftJoin('plus_card_credi_and_puan_add as last_transaction', function($join) {
                $join->on('last_transaction.card_id', '=', 'plus_cards.id')
                     ->whereRaw('last_transaction.id = (SELECT MAX(id) FROM plus_card_credi_and_puan_add WHERE card_id = plus_cards.id AND deleted_at IS NULL)');
            })
            ->leftJoin('users as last_transaction_user', 'last_transaction_user.id', '=', 'last_transaction.user_id')
            ->leftJoin('branches as last_transaction_branch', 'last_transaction_branch.id', '=', 'last_transaction_user.branch_id')
            ->whereNull('plus_cards.deleted_at');

        // Branch yetkilendirmesi
        if ($branchIds !== null && !empty($branchIds)) {
            $query->whereIn('plus_cards.branch_id', $branchIds);
        }

        return $query->groupBy(
                'plus_cards.customer_id',
                'customers.ad',
                'customers.soyad',
                'customers.telefon',
                'customers.unvan'
            )
            ->havingRaw('COUNT(plus_cards.customer_id) > 1')
            ->paginate($perPage);
    }

    /**
     * Bir müşteri ve kart için plus_cards tablosunda not kaydeder
     */
    public function saveCustomerNote($customerId, $note, $key = null, $cardNo)
    {
        $plusCard = PlusCard::where('customer_id', $customerId)
            ->where('no', $cardNo)
            ->first();
        if ($plusCard) {
            $plusCard->note = $note;
            $plusCard->save();
            return true;
        }
        return false;
    }
}